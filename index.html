<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>吵架包赢</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Inter', sans-serif;
        /* Subtle dotted background pattern */
        background-image: radial-gradient(circle, rgba(0, 0, 0, 0.04) 0.8px, transparent 0.8px);
        background-size: 12px 12px;
      }
      .chat-scrollbar::-webkit-scrollbar {
        width: 6px;
      }
      .chat-scrollbar::-webkit-scrollbar-thumb {
        background-color: #cbd5e1; /* cool-gray-300 */
        border-radius: 3px;
      }
      .chat-scrollbar::-webkit-scrollbar-track {
        background-color: #f1f5f9; /* cool-gray-100 */
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .animate-fade-in-up {
        animation: fadeInUp 0.3s ease-out forwards;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "@google/genai": "https://esm.sh/@google/genai@^1.0.1",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
</head>
  <body class="bg-slate-100">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
