// This file should be placed in the /api directory at the root of your project.
// For example: /api/gemini.ts

import { GoogleGenAI, GenerateContentResponse, Content } from '@google/genai';
import { GEMINI_MODEL_NAME } from '../constants'; // Assuming constants.ts is in the parent directory
import type { ApiChatMessage, GeminiApiRequestBody } from '../types'; // Assuming types.ts is in the parent directory

// This function will be deployed as a Vercel Serverless Function.
// Vercel automatically picks up files in the /api directory.

// Ensure API_KEY is available in the Vercel environment variables
const apiKey = process.env.API_KEY;
let genAI: GoogleGenAI | null = null;

if (apiKey) {
  genAI = new GoogleGenAI({ apiKey });
} else {
  console.error("CRITICAL: API_KEY environment variable is not set in Vercel. API will not function.");
}

export default async function handler(req: Request): Promise<Response> {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (!genAI) {
    // This check is crucial. If the API key wasn't set, genAI would be null.
    return new Response(JSON.stringify({ error: 'API key not configured on server or server failed to initialize AI service.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const { history, newMessage, systemInstruction } = await req.json() as GeminiApiRequestBody;

    const contents: Content[] = [
      ...history,
      { role: 'user', parts: [{ text: newMessage }] },
    ];
    
    // Corrected: Call generateContent directly from genAI.models
    const result: GenerateContentResponse = await genAI.models.generateContent({
      model: GEMINI_MODEL_NAME,
      contents: contents,
      config: {
        systemInstruction: systemInstruction,
        // Add other generation configs if needed, e.g., temperature, topK, topP
      },
    });
    
    const textResponse = result.text;

    if (typeof textResponse === 'string') {
      return new Response(JSON.stringify({ text: textResponse }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    } else {
      console.warn("AI response format unexpected, 'text' property not found or not a string:", result);
      return new Response(JSON.stringify({ error: 'AI response format was unexpected.' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

  } catch (error) {
    console.error('Error in /api/gemini function:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(JSON.stringify({ error: `Failed to get AI response: ${errorMessage}` }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
