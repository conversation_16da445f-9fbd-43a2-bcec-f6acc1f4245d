// This file should be placed in the /api directory at the root of your project.
// For example: /api/gemini.ts

import { GoogleGenAI } from '@google/genai';
import type { VercelRequest, VercelResponse } from '@vercel/node';

// This function will be deployed as a Vercel Serverless Function.
// Vercel automatically picks up files in the /api directory.

// Define types locally to avoid import issues
interface ApiChatMessage {
  role: 'user' | 'model';
  parts: { text: string }[];
}

interface GeminiApiRequestBody {
  history: ApiChatMessage[];
  newMessage: string;
  systemInstruction: string;
}

// Constants
const GEMINI_MODEL_NAME = 'gemini-2.0-flash-exp';

// Ensure GEMINI_API_KEY is available in the Vercel environment variables
const apiKey = process.env.GEMINI_API_KEY;

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method Not Allowed' });
    return;
  }

  if (!apiKey) {
    console.error('GEMINI_API_KEY not configured');
    res.status(500).json({ error: 'API key not configured on server.' });
    return;
  }

  try {
    // Initialize Gemini AI inside the function to avoid module-level issues
    const genAI = new GoogleGenAI({ apiKey });

    const { history, newMessage, systemInstruction } = req.body as GeminiApiRequestBody;

    const contents = [
      ...history,
      { role: 'user', parts: [{ text: newMessage }] },
    ];

    // Use the correct API call for @google/genai
    const result = await genAI.models.generateContent({
      model: GEMINI_MODEL_NAME,
      contents: contents,
      config: {
        systemInstruction: systemInstruction,
      },
    });

    const textResponse = result.text;

    if (typeof textResponse === 'string') {
      res.status(200).json({ text: textResponse });
      return;
    } else {
      console.warn("AI response format unexpected:", result);
      res.status(500).json({ error: 'AI response format was unexpected.' });
      return;
    }

  } catch (error) {
    console.error('Error in /api/gemini function:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({ error: `Failed to get AI response: ${errorMessage}` });
    return;
  }
}
