// This file should be placed in the /api directory at the root of your project.
// For example: /api/gemini.ts

import { GoogleGenAI, GenerateContentResponse, Content } from '@google/genai';
import { GEMINI_MODEL_NAME } from '../constants'; // Assuming constants.ts is in the parent directory
import type { ApiChatMessage, GeminiApiRequestBody } from '../types'; // Assuming types.ts is in the parent directory
import type { VercelRequest, VercelResponse } from '@vercel/node';

// This function will be deployed as a Vercel Serverless Function.
// Vercel automatically picks up files in the /api directory.

// Ensure GEMINI_API_KEY is available in the Vercel environment variables
const apiKey = process.env.GEMINI_API_KEY;
let genAI: GoogleGenAI | null = null;

if (apiKey) {
  genAI = new GoogleGenAI({ apiKey });
} else {
  console.error("CRITICAL: GEMINI_API_KEY environment variable is not set in Vercel. API will not function.");
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method Not Allowed' });
    return;
  }

  if (!genAI) {
    console.error('GEMINI_API_KEY not configured');
    res.status(500).json({ error: 'API key not configured on server or server failed to initialize AI service.' });
    return;
  }

  try {
    const { history, newMessage, systemInstruction } = req.body as GeminiApiRequestBody;

    const contents: Content[] = [
      ...history,
      { role: 'user', parts: [{ text: newMessage }] },
    ];

    // Corrected: Call generateContent directly from genAI.models
    const result: GenerateContentResponse = await genAI.models.generateContent({
      model: GEMINI_MODEL_NAME,
      contents: contents,
      config: {
        systemInstruction: systemInstruction,
        // Add other generation configs if needed, e.g., temperature, topK, topP
      },
    });

    const textResponse = result.text;

    if (typeof textResponse === 'string') {
      res.status(200).json({ text: textResponse });
      return;
    } else {
      console.warn("AI response format unexpected, 'text' property not found or not a string:", result);
      res.status(500).json({ error: 'AI response format was unexpected.' });
      return;
    }

  } catch (error) {
    console.error('Error in /api/gemini function:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({ error: `Failed to get AI response: ${errorMessage}` });
    return;
  }
}
