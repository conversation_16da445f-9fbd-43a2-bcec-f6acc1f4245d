import React, { useState, useEffect } from 'react';
import { ChatMessage, MessageSender } from '../types';

interface DebateStatsProps {
  messages: ChatMessage[];
  startTime: Date;
  currentTopic: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const DebateStats: React.FC<DebateStatsProps> = ({
  messages,
  startTime,
  currentTopic,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // 更新当前时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 计算统计数据
  const userMessages = messages.filter(msg => msg.sender === MessageSender.USER);
  const aiMessages = messages.filter(msg => msg.sender === MessageSender.AI);
  const totalRounds = Math.max(userMessages.length, aiMessages.length);
  
  // 计算辩论时长
  const duration = Math.floor((currentTime.getTime() - startTime.getTime()) / 1000);
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;

  // 计算平均回复时间（简化版）
  const avgResponseTime = messages.length > 1 ? Math.floor(duration / messages.length) : 0;

  // 计算字数统计
  const userWordCount = userMessages.reduce((total, msg) => total + msg.text.length, 0);
  const aiWordCount = aiMessages.reduce((total, msg) => total + msg.text.length, 0);

  if (isCollapsed) {
    return (
      <div className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg p-3 shadow-sm">
        <button
          onClick={onToggleCollapse}
          className="flex items-center justify-between w-full text-left"
        >
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">
              辩论进行中 • {totalRounds} 轮 • {minutes}:{seconds.toString().padStart(2, '0')}
            </span>
          </div>
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg p-4 shadow-lg">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <h3 className="text-lg font-semibold text-gray-800">辩论统计</h3>
        </div>
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
            </svg>
          </button>
        )}
      </div>

      {/* 主要统计 */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{totalRounds}</div>
          <div className="text-sm text-blue-700">辩论轮次</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {minutes}:{seconds.toString().padStart(2, '0')}
          </div>
          <div className="text-sm text-green-700">持续时间</div>
        </div>
      </div>

      {/* 详细统计 */}
      <div className="space-y-3">
        <div className="flex justify-between items-center py-2 border-b border-gray-100">
          <span className="text-sm text-gray-600">当前主题</span>
          <span className="text-sm font-medium text-gray-800 max-w-32 truncate" title={currentTopic}>
            {currentTopic}
          </span>
        </div>
        
        <div className="flex justify-between items-center py-2 border-b border-gray-100">
          <span className="text-sm text-gray-600">你的发言</span>
          <span className="text-sm font-medium text-gray-800">
            {userMessages.length} 次 • {userWordCount} 字
          </span>
        </div>
        
        <div className="flex justify-between items-center py-2 border-b border-gray-100">
          <span className="text-sm text-gray-600">AI 回复</span>
          <span className="text-sm font-medium text-gray-800">
            {aiMessages.length} 次 • {aiWordCount} 字
          </span>
        </div>
        
        <div className="flex justify-between items-center py-2">
          <span className="text-sm text-gray-600">平均回复</span>
          <span className="text-sm font-medium text-gray-800">
            {avgResponseTime}秒/轮
          </span>
        </div>
      </div>

      {/* 进度条 */}
      <div className="mt-4 pt-3 border-t border-gray-100">
        <div className="flex justify-between text-xs text-gray-500 mb-1">
          <span>辩论激烈度</span>
          <span>{Math.min(100, totalRounds * 10)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${Math.min(100, totalRounds * 10)}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};
