import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON> } from '../types';
import { HeroSection } from './HeroSection';

interface ScenarioSelectorProps {
  scenarios: Scenario[];
  onSelectScenario: (scenario: Scenario) => void;
  isDarkMode?: boolean;
}

export const ScenarioSelector: React.FC<ScenarioSelectorProps> = ({ scenarios, onSelectScenario, isDarkMode = false }) => {
  const [customPrompt, setCustomPrompt] = useState('');

  const handleCustomSubmit = () => {
    if (customPrompt.trim() === '') {
      return;
    }
    const customScenario: Scenario = {
      id: `custom-${Date.now()}`,
      title: '自定义辩题',
      prompt: customPrompt.trim(),
      // No icon for custom scenarios by default, or could add a generic one
    };
    onSelectScenario(customScenario);
    setCustomPrompt('');
  };

  return (
    <div className="flex flex-col h-full overflow-y-auto chat-scrollbar">
      {/* 英雄区域 */}
      <HeroSection isDarkMode={isDarkMode} />

      {/* 选择区域 */}
      <div className={`flex flex-col items-center justify-start p-4 sm:p-6 md:p-8 ${
        isDarkMode ? 'bg-gray-800/50' : 'bg-slate-50/20'
      }`}>
        <h2 className={`text-xl sm:text-2xl font-semibold mb-6 sm:mb-8 text-center tracking-tight ${
          isDarkMode ? 'text-white' : 'text-slate-700'
        }`}>
          选择你的辩题！
        </h2>

      {/* Predefined Scenarios */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full max-w-lg">
        {scenarios.map((scenario) => {
          const IconComponent = scenario.icon;
          return (
            <button
              key={scenario.id}
              onClick={() => onSelectScenario(scenario)}
              className={`p-4 border rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-[1.03] transition-all duration-250 ease-in-out text-left group h-full flex flex-col justify-between ${
                isDarkMode
                  ? 'bg-gray-700/80 border-gray-600/70 hover:bg-gray-600/80 hover:border-blue-400/70'
                  : 'bg-white/80 border-slate-300/70 hover:bg-blue-50/80 hover:border-blue-400/70'
              }`}
            >
              <div>
                <div className="flex items-center mb-2">
                  {IconComponent && (
                    <IconComponent className="w-6 h-6 mr-3 text-slate-500 group-hover:text-blue-600 transition-colors duration-150" />
                  )}
                  <h3 className={`text-md sm:text-lg font-medium group-hover:text-blue-700 transition-colors duration-150 flex-1 ${
                    isDarkMode ? 'text-white' : 'text-slate-800'
                  }`}>
                    {scenario.title}
                  </h3>
                </div>
                <p className={`text-xs sm:text-sm mt-1 line-clamp-3 group-hover:text-blue-600 transition-colors duration-150 ${
                  isDarkMode ? 'text-gray-300' : 'text-slate-500'
                }`}>
                  {scenario.prompt}
                </p>
              </div>
            </button>
          );
        })}
      </div>

      {/* Divider */}
      <div className="w-full max-w-lg my-6 sm:my-8">
        <div className="relative">
          <div className="absolute inset-0 flex items-center" aria-hidden="true">
            <div className={`w-full border-t ${
              isDarkMode ? 'border-gray-600/80' : 'border-slate-300/80'
            }`} />
          </div>
          <div className="relative flex justify-center">
            <span className={`px-3 text-sm ${
              isDarkMode
                ? 'bg-gray-800/50 text-gray-400'
                : 'bg-slate-50/20 text-slate-500'
            }`}>或者</span>
          </div>
        </div>
      </div>

      {/* Custom Scenario Input */}
      <div className="w-full max-w-lg mb-6">
        <h3 className={`text-lg font-medium mb-3 text-center ${
          isDarkMode ? 'text-white' : 'text-slate-700'
        }`}>创建你自己的辩题：</h3>
        <textarea
          value={customPrompt}
          onChange={(e) => setCustomPrompt(e.target.value)}
          placeholder="在此输入你的辩题陈述或你想争论的初始观点..."
          rows={3}
          className={`w-full p-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-shadow duration-150 ease-in-out shadow-sm hover:shadow-md resize-none ${
            isDarkMode
              ? 'border-gray-600/80 bg-gray-700/70 text-white placeholder-gray-400'
              : 'border-slate-300/80 bg-white/70 text-gray-900 placeholder-gray-500'
          }`}
          aria-label="自定义辩题输入框"
        />
        <button
          onClick={handleCustomSubmit}
          disabled={!customPrompt.trim()}
          className="mt-3 w-full p-3 bg-green-600 text-white rounded-xl hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-95 transition-all duration-200 ease-in-out shadow-md hover:shadow-lg disabled:bg-slate-400 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          开始自定义辩论
        </button>
      </div>

        <p className={`mt-auto pt-4 text-xs text-center ${
          isDarkMode ? 'text-gray-400' : 'text-slate-500'
        }`}>
          选择一个预设话题，或创建你自己的话题，与我们极具主见的 AI 开始辩论。
        </p>
      </div>
    </div>
  );
};