import React from 'react';
import { AppLogo } from './AppLogo';

interface EmptyStateProps {
  title?: string;
  description?: string;
  isDarkMode?: boolean;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = "准备好开始辩论了吗？",
  description = "选择一个话题，让我们来一场精彩的思辨对决！",
  isDarkMode = false,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      {/* 背景装饰 */}
      <div className="relative mb-6">
        <div className={`absolute inset-0 w-32 h-32 rounded-full ${
          isDarkMode ? 'bg-gray-700/30' : 'bg-blue-100/50'
        } blur-xl`}></div>
        <AppLogo size="large" showText={false} className="relative z-10" />
      </div>

      {/* 标题 */}
      <h3 className={`text-xl font-semibold mb-3 ${
        isDarkMode ? 'text-white' : 'text-gray-800'
      }`}>
        {title}
      </h3>

      {/* 描述 */}
      <p className={`text-sm max-w-md leading-relaxed ${
        isDarkMode ? 'text-gray-300' : 'text-gray-600'
      }`}>
        {description}
      </p>

      {/* 动画提示 */}
      <div className="mt-6 flex items-center space-x-2 animate-bounce">
        <div className={`w-2 h-2 rounded-full ${
          isDarkMode ? 'bg-blue-400' : 'bg-blue-500'
        }`}></div>
        <div className={`w-2 h-2 rounded-full ${
          isDarkMode ? 'bg-purple-400' : 'bg-purple-500'
        } delay-75`}></div>
        <div className={`w-2 h-2 rounded-full ${
          isDarkMode ? 'bg-pink-400' : 'bg-pink-500'
        } delay-150`}></div>
      </div>
    </div>
  );
};
