import React from 'react';

interface QuickReplyButtonsProps {
  onQuickReply: (text: string) => void;
  currentTopic: string;
  className?: string;
}

// 根据不同主题提供不同的快速回复模板
const getQuickReplies = (topic: string): string[] => {
  const commonReplies = [
    "你这个观点站不住脚",
    "有什么证据支持你的说法？",
    "这个逻辑有明显漏洞",
    "让我们回到核心问题",
    "你忽略了一个重要因素"
  ];

  const topicSpecificReplies: { [key: string]: string[] } = {
    "智能手机与智力": [
      "手机只是工具，关键看怎么用",
      "你混淆了依赖和便利的概念",
      "科技进步从来都是双刃剑",
      "这是时代发展的必然趋势"
    ],
    "社交媒体的影响": [
      "你只看到了负面影响",
      "社交媒体也促进了信息传播",
      "问题在于使用方式，不是媒体本身",
      "这扩大了人们的社交圈子"
    ],
    "人工智能的未来": [
      "AI 是人类智慧的延伸",
      "你对 AI 的理解过于狭隘",
      "技术发展不可阻挡",
      "关键是如何规范和引导"
    ],
    "环保与经济发展": [
      "环保和经济可以共赢",
      "短期成本换长期收益",
      "你忽略了技术创新的作用",
      "这是可持续发展的必由之路"
    ],
    "教育制度改革": [
      "一刀切的改革不现实",
      "你忽略了现实约束条件",
      "教育需要循序渐进",
      "理想很丰满，现实很骨感"
    ],
    "远程工作趋势": [
      "效率不能只看表面",
      "你忽略了协作的重要性",
      "不同工作性质要区别对待",
      "平衡是关键"
    ]
  };

  const specificReplies = topicSpecificReplies[topic] || [];
  return [...commonReplies, ...specificReplies];
};

export const QuickReplyButtons: React.FC<QuickReplyButtonsProps> = ({
  onQuickReply,
  currentTopic,
  className = ""
}) => {
  const quickReplies = getQuickReplies(currentTopic);

  return (
    <div className={`${className}`}>
      <div className="mb-2">
        <span className="text-xs text-gray-500 font-medium">💡 快速回复</span>
      </div>
      <div className="flex flex-wrap gap-2 max-h-24 overflow-y-auto">
        {quickReplies.slice(0, 8).map((reply, index) => (
          <button
            key={index}
            onClick={() => onQuickReply(reply)}
            className="px-3 py-1.5 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-full border border-blue-200 transition-all duration-200 hover:scale-105 active:scale-95 hover:shadow-md"
            title={`点击发送: ${reply}`}
          >
            {reply}
          </button>
        ))}
      </div>
    </div>
  );
};
