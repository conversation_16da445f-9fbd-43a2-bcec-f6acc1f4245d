import React, { useState, useEffect } from 'react';
import { ChatMessage, MessageSender } from '../types';
import { TypewriterText } from './TypewriterText';

interface AnimatedMessageProps {
  message: ChatMessage;
  isLatest?: boolean;
  showTypingEffect?: boolean;
  onTypingComplete?: () => void;
}

export const AnimatedMessage: React.FC<AnimatedMessageProps> = ({
  message,
  isLatest = false,
  showTypingEffect = false,
  onTypingComplete
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const isUser = message.sender === MessageSender.USER;
  const isAI = message.sender === MessageSender.AI;

  useEffect(() => {
    // 延迟显示动画
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div
      className={`flex mb-4 transition-all duration-500 ease-out transform ${
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
      } ${isUser ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* 头像 */}
        <div className="flex-shrink-0 mx-2">
          {isUser ? (
            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white text-sm font-bold">你</span>
            </div>
          ) : (
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
          )}
        </div>

        {/* 消息内容 */}
        <div className="flex flex-col">
          <div
            className={`px-4 py-3 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl transform hover:scale-[1.02] ${
              isUser
                ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-br-sm'
                : 'bg-white border border-gray-200 text-gray-800 rounded-bl-sm'
            } ${message.isError ? 'border-red-300 bg-red-50 text-red-700' : ''}`}
          >
            {/* 消息文本 */}
            <div className="text-sm leading-relaxed">
              {showTypingEffect && isAI && isLatest ? (
                <TypewriterText
                  text={message.text}
                  speed={30}
                  onComplete={onTypingComplete}
                  className="block"
                />
              ) : (
                <span className="block">{message.text}</span>
              )}
            </div>
          </div>

          {/* 时间戳 */}
          <div className={`text-xs text-gray-500 mt-1 px-2 ${isUser ? 'text-right' : 'text-left'}`}>
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    </div>
  );
};
