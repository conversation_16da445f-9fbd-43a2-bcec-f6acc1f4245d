import React from 'react';

interface AppLogoProps {
  size?: 'small' | 'medium' | 'large' | 'hero';
  className?: string;
  showText?: boolean;
}

export const AppLogo: React.FC<AppLogoProps> = ({
  size = 'medium',
  className = '',
  showText = true
}) => {
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-12 h-12',
    large: 'w-24 h-24',
    hero: 'w-32 h-32 sm:w-40 sm:h-40'
  };

  const textSizeClasses = {
    small: 'text-sm',
    medium: 'text-lg',
    large: 'text-2xl',
    hero: 'text-3xl sm:text-4xl'
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className={`${sizeClasses[size]} rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 p-1`}>
        <img
          src="/cover-image.jpeg"
          alt="吵架包赢"
          className="w-full h-full object-cover rounded-lg"
          onError={(e) => {
            // 如果图片加载失败，显示文字替代
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = '<div class="w-full h-full flex items-center justify-center text-white font-bold text-xs">吵架</div>';
            }
          }}
        />
      </div>
      {showText && (
        <span className={`font-bold text-gray-800 ${textSizeClasses[size]}`}>
          吵架包赢
        </span>
      )}
    </div>
  );
};
