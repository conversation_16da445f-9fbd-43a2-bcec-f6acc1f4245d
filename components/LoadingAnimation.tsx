import React from 'react';

interface LoadingAnimationProps {
  message?: string;
  className?: string;
}

export const LoadingAnimation: React.FC<LoadingAnimationProps> = ({
  message = "AI 正在思考中...",
  className = ""
}) => {
  return (
    <div className={`flex items-center space-x-3 p-4 ${className}`}>
      {/* AI 头像区域 */}
      <div className="flex-shrink-0">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white text-sm font-bold">AI</span>
        </div>
      </div>
      
      {/* 消息气泡 */}
      <div className="bg-gray-100 rounded-2xl rounded-bl-sm px-4 py-3 max-w-xs animate-fade-in-up">
        <div className="flex items-center space-x-2">
          {/* 跳动的点 */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
          <span className="text-sm text-gray-600 animate-pulse">{message}</span>
        </div>
      </div>
    </div>
  );
};

// 脑力激荡加载动画
export const ThinkingAnimation: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <div className="relative">
        {/* 大脑图标 */}
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full animate-pulse flex items-center justify-center">
          <span className="text-white text-xs">🧠</span>
        </div>
        
        {/* 思考波纹 */}
        <div className="absolute inset-0 rounded-full border-2 border-purple-300 animate-ping"></div>
        <div className="absolute inset-0 rounded-full border-2 border-purple-200 animate-ping" style={{ animationDelay: '0.5s' }}></div>
      </div>
      
      <div className="text-sm text-gray-600 animate-pulse">
        正在组织犀利的反驳...
      </div>
    </div>
  );
};

// 简单的加载点动画
export const DotsLoading: React.FC<{ className?: string }> = ({ className = "" }) => {
  return (
    <div className={`flex space-x-1 ${className}`}>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
      <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
    </div>
  );
};
