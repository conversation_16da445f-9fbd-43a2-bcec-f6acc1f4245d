import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage, MessageSender } from '../types';
import { AnimatedMessage } from './AnimatedMessage';
import { LoadingAnimation, ThinkingAnimation } from './LoadingAnimation';
import { EmptyState } from './EmptyState';
import { SendIcon } from './icons/SendIcon';
import { StopIcon } from './icons/StopIcon';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  onEndArgument: () => void;
  currentTopic: string;
  onEditMessage?: (messageId: string, newText: string) => void;
  onDeleteMessage?: (messageId: string) => void;
  isDarkMode?: boolean;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ messages, onSendMessage, isLoading, onEndArgument, currentTopic, onEditMessage, onDeleteMessage, isDarkMode = false }) => {
  const [inputText, setInputText] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() && !isLoading) {
      onSendMessage(inputText);
      setInputText('');
    }
  };

  // 检查最新消息是否需要打字机效果
  const latestMessage = messages[messages.length - 1];
  const shouldShowTyping = latestMessage && latestMessage.sender === MessageSender.AI && !isTyping;

  // 当新的 AI 消息到达时，开始打字效果
  useEffect(() => {
    if (shouldShowTyping) {
      setIsTyping(true);
    }
  }, [shouldShowTyping]);

  const handleTypingComplete = () => {
    setIsTyping(false);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow p-3 sm:p-4 md:p-6 space-y-3 sm:space-y-4 overflow-y-auto chat-scrollbar">
        {messages.length === 0 ? (
          <EmptyState
            title="开始你的辩论之旅"
            description="输入你的第一个观点，让 AI 来挑战你的思维！"
            isDarkMode={isDarkMode}
            className="h-full"
          />
        ) : (
          messages.map((msg, index) => {
          const isLatest = index === messages.length - 1;
          const showTypingEffect = isLatest && msg.sender === MessageSender.AI && isTyping;

          return (
            <AnimatedMessage
              key={msg.id}
              message={msg}
              isLatest={isLatest}
              showTypingEffect={showTypingEffect}
              onTypingComplete={handleTypingComplete}
              onEditMessage={onEditMessage}
              onDeleteMessage={onDeleteMessage}
            />
          );
        })
        )}

        {/* 加载动画 - 当 AI 正在思考时显示 */}
        {isLoading && (
          <div className="animate-fade-in-up">
            <LoadingAnimation message="AI 正在组织反驳论点..." />
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
      <div className="p-3 sm:p-4 border-t border-slate-200/80 bg-slate-50/60 rounded-b-2xl">
        <form onSubmit={handleSubmit} className="flex items-center space-x-2 sm:space-x-3">
          <button
            type="button"
            onClick={onEndArgument}
            title="结束当前辩论并选择新话题"
            className="p-2 text-slate-500 hover:text-red-600 active:text-red-700 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-400 rounded-full transform hover:scale-105 active:scale-95"
            aria-label="结束辩论"
          >
            <StopIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </button>
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="输入你的论点..."
            className="flex-grow p-3 border border-slate-300/80 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 ease-in-out shadow-sm hover:shadow-md focus:shadow-lg disabled:bg-slate-100 bg-white/80"
            disabled={isLoading}
            aria-label="聊天输入框"
          />
          <button
            type="submit"
            disabled={isLoading || !inputText.trim()}
            className="p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 active:bg-blue-800 transform hover:scale-105 active:scale-95 transition-all duration-150 shadow-md disabled:bg-slate-400 disabled:cursor-not-allowed disabled:hover:scale-100"
            aria-label="发送消息"
          >
            <SendIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </button>
        </form>
      </div>
    </div>
  );
};