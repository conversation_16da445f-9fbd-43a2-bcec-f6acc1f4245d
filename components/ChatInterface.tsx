import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage } from '../types';
import { MessageBubble } from './MessageBubble';
import { SendIcon } from './icons/SendIcon';
import { StopIcon } from './icons/StopIcon'; 

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  onEndArgument: () => void;
  currentTopic: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ messages, onSendMessage, isLoading, onEndArgument, currentTopic }) => {
  const [inputText, setInputText] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() && !isLoading) {
      onSendMessage(inputText);
      setInputText('');
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow p-3 sm:p-4 md:p-6 space-y-3 sm:space-y-4 overflow-y-auto chat-scrollbar">
        {messages.map((msg) => (
          <MessageBubble key={msg.id} message={msg} />
        ))}
        {isLoading && messages.length > 0 && messages[messages.length -1].sender === 'user' && (
          <div className="flex justify-start animate-fade-in-up">
             <div className="bg-slate-200/80 text-slate-700 p-3 rounded-lg max-w-xs lg:max-w-md animate-pulse shadow-md">
                思考中...
             </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="p-3 sm:p-4 border-t border-slate-200/80 bg-slate-50/60 rounded-b-2xl">
        <form onSubmit={handleSubmit} className="flex items-center space-x-2 sm:space-x-3">
          <button
            type="button"
            onClick={onEndArgument}
            title="结束当前辩论并选择新话题"
            className="p-2 text-slate-500 hover:text-red-600 active:text-red-700 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-400 rounded-full transform hover:scale-105 active:scale-95"
            aria-label="结束辩论"
          >
            <StopIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </button>
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="输入你的论点..."
            className="flex-grow p-3 border border-slate-300/80 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-150 ease-in-out shadow-sm hover:shadow-md focus:shadow-lg disabled:bg-slate-100 bg-white/80"
            disabled={isLoading}
            aria-label="聊天输入框"
          />
          <button
            type="submit"
            disabled={isLoading || !inputText.trim()}
            className="p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 active:bg-blue-800 transform hover:scale-105 active:scale-95 transition-all duration-150 shadow-md disabled:bg-slate-400 disabled:cursor-not-allowed disabled:hover:scale-100"
            aria-label="发送消息"
          >
            <SendIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </button>
        </form>
      </div>
    </div>
  );
};