import React from 'react';
import { AppLogo } from './AppLogo';

interface HeroSectionProps {
  isDarkMode?: boolean;
  className?: string;
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  isDarkMode = false,
  className = ''
}) => {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* 背景图片 */}
      <div className="absolute inset-0">
        <img
          src="/cover-image.jpeg"
          alt="吵架包赢背景"
          className="w-full h-full object-cover"
        />
        {/* 渐变遮罩 */}
        <div className={`absolute inset-0 ${
          isDarkMode 
            ? 'bg-gradient-to-br from-gray-900/80 via-gray-800/70 to-slate-900/80' 
            : 'bg-gradient-to-br from-white/80 via-blue-50/70 to-purple-50/80'
        }`}></div>
      </div>

      {/* 内容 */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-[300px] p-8 text-center">
        {/* Logo */}
        <div className="mb-6">
          <AppLogo size="hero" showText={false} />
        </div>

        {/* 标题 */}
        <h1 className={`text-4xl sm:text-5xl md:text-6xl font-bold mb-4 ${
          isDarkMode ? 'text-white' : 'text-gray-800'
        }`}>
          吵架包赢
        </h1>

        {/* 副标题 */}
        <p className={`text-lg sm:text-xl md:text-2xl mb-6 max-w-2xl ${
          isDarkMode ? 'text-gray-200' : 'text-gray-600'
        }`}>
          与 AI 进行激烈辩论，锻炼你的逻辑思维和表达能力
        </p>

        {/* 特色标签 */}
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          {[
            '🧠 智能对话',
            '⚡ 实时辩论', 
            '🎯 多种主题',
            '🏆 包赢策略'
          ].map((tag, index) => (
            <span
              key={index}
              className={`px-4 py-2 rounded-full text-sm font-medium ${
                isDarkMode 
                  ? 'bg-gray-700/80 text-gray-200 border border-gray-600' 
                  : 'bg-white/80 text-gray-700 border border-gray-200'
              } backdrop-blur-sm shadow-lg`}
            >
              {tag}
            </span>
          ))}
        </div>

        {/* 开始提示 */}
        <div className={`text-sm ${
          isDarkMode ? 'text-gray-300' : 'text-gray-500'
        } animate-pulse`}>
          选择一个辩论主题开始挑战 AI 👇
        </div>
      </div>

      {/* 装饰性元素 */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
    </div>
  );
};
