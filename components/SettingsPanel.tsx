import React from 'react';
import { ThemeToggle } from './ThemeToggle';

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
  onToggleDarkMode: () => void;
  aiPersonality: 'aggressive' | 'moderate' | 'friendly';
  onPersonalityChange: (personality: 'aggressive' | 'moderate' | 'friendly') => void;
  typingSpeed: number;
  onTypingSpeedChange: (speed: number) => void;
  showQuickReplies: boolean;
  onToggleQuickReplies: () => void;
  showStats: boolean;
  onToggleStats: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  isDarkMode,
  onToggleDarkMode,
  aiPersonality,
  onPersonalityChange,
  typingSpeed,
  onTypingSpeedChange,
  showQuickReplies,
  onToggleQuickReplies,
  showStats,
  onToggleStats
}) => {
  if (!isOpen) return null;

  const personalityOptions = [
    { value: 'aggressive', label: '激进辩手', desc: '犀利尖锐，不留情面' },
    { value: 'moderate', label: '理性辩手', desc: '逻辑清晰，有理有据' },
    { value: 'friendly', label: '友善辩手', desc: '温和讨论，循循善诱' }
  ] as const;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">设置</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 设置内容 */}
        <div className="p-6 space-y-6">
          {/* 主题设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">外观设置</h3>
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-gray-700">深色模式</span>
                <p className="text-xs text-gray-500">切换明暗主题</p>
              </div>
              <ThemeToggle isDark={isDarkMode} onToggle={onToggleDarkMode} />
            </div>
          </div>

          {/* AI 性格设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">AI 辩手性格</h3>
            <div className="space-y-3">
              {personalityOptions.map((option) => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="personality"
                    value={option.value}
                    checked={aiPersonality === option.value}
                    onChange={(e) => onPersonalityChange(e.target.value as any)}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-700">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 打字速度设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">打字速度</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>慢</span>
                <span>快</span>
              </div>
              <input
                type="range"
                min="10"
                max="100"
                value={101 - typingSpeed}
                onChange={(e) => onTypingSpeedChange(101 - parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="text-xs text-gray-500 text-center">
                当前速度: {typingSpeed}ms/字符
              </div>
            </div>
          </div>

          {/* 功能开关 */}
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">功能设置</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">快速回复</span>
                  <p className="text-xs text-gray-500">显示常用回复按钮</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showQuickReplies}
                    onChange={onToggleQuickReplies}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">辩论统计</span>
                  <p className="text-xs text-gray-500">显示实时统计面板</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showStats}
                    onChange={onToggleStats}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            完成设置
          </button>
        </div>
      </div>
    </div>
  );
};
