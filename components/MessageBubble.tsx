import React from 'react';
import { ChatMessage, MessageSender } from '../types';
import { UserAvatarIcon } from './icons/UserAvatarIcon';
import { AiAvatarIcon } from './icons/AiAvatarIcon';

interface MessageBubbleProps {
  message: ChatMessage;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.sender === MessageSender.USER;
  
  const bubbleClasses = isUser
    ? 'bg-blue-600 text-white rounded-tr-none'
    : 'bg-slate-200 text-slate-800 rounded-tl-none';
  
  const alignmentClass = isUser ? 'justify-end' : 'justify-start';
  const avatarOrderClass = isUser ? 'order-2' : 'order-1';
  const messageOrderClass = isUser ? 'order-1 mr-2' : 'order-2 ml-2';
  
  const errorClass = message.isError ? 'border-2 border-red-400 bg-red-100 text-red-700 shadow-md' : 'shadow-md hover:shadow-lg';

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const AvatarComponent = isUser ? UserAvatarIcon : AiAvatarIcon;
  const avatarColor = isUser ? 'text-blue-500' : 'text-slate-500';

  return (
    <div className={`flex w-full animate-fade-in-up ${alignmentClass} items-end space-x-2`}>
      {!isUser && (
        <div className={`flex-shrink-0 ${avatarOrderClass}`}>
          <AvatarComponent className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full p-0.5 ${avatarColor} bg-white shadow-sm border border-slate-200`} />
        </div>
      )}
      <div 
        className={`p-3 rounded-xl max-w-[70%] sm:max-w-[75%] break-words transition-shadow duration-200 ease-in-out ${bubbleClasses} ${errorClass} ${messageOrderClass}`}
        role="log" 
        aria-live={message.sender === MessageSender.AI ? "polite" : "off"}
        aria-atomic="true"
      >
        <p className="text-sm whitespace-pre-wrap">{message.text}</p>
        <p className={`text-xs mt-1 ${isUser ? 'text-blue-200/80' : 'text-slate-500/80'} text-right`}>
          {formatTime(message.timestamp)}
        </p>
      </div>
      {isUser && (
         <div className={`flex-shrink-0 ${avatarOrderClass}`}>
          <AvatarComponent className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full p-0.5 ${avatarColor} bg-white shadow-sm border border-slate-200`} />
        </div>
      )}
    </div>
  );
};