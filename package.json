{"name": "吵架包赢", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"vite\"", "server": "node server.js", "client": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0", "concurrently": "^8.2.2"}}