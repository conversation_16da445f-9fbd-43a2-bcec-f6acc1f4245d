import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { GoogleGenAI } from '@google/genai';

// Load environment variables
dotenv.config({ path: '.env.local' });

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize Gemini AI
const apiKey = process.env.GEMINI_API_KEY;
let genAI = null;

if (apiKey) {
  genAI = new GoogleGenAI({ apiKey });
  console.log('✅ Gemini AI initialized successfully');
} else {
  console.error('❌ GEMINI_API_KEY not found in environment variables');
}

// API endpoint
app.post('/api/gemini', async (req, res) => {
  if (!genAI) {
    return res.status(500).json({
      error: 'API key not configured on server or server failed to initialize AI service.'
    });
  }

  try {
    const { history, newMessage, systemInstruction } = req.body;

    const contents = [
      ...history,
      { role: 'user', parts: [{ text: newMessage }] },
    ];

    // Use the correct API method for the @google/genai package
    const result = await genAI.models.generateContent({
      model: 'gemini-2.0-flash-exp',
      contents: contents,
      config: {
        systemInstruction: systemInstruction,
      },
    });

    const textResponse = result.text;

    if (typeof textResponse === 'string') {
      return res.json({ text: textResponse });
    } else {
      console.warn("AI response format unexpected, 'text' property not found or not a string:", result);
      return res.status(500).json({ error: 'AI response format was unexpected.' });
    }

  } catch (error) {
    console.error('Error in /api/gemini function:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return res.status(500).json({ error: `Failed to get AI response: ${errorMessage}` });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', geminiConfigured: !!genAI });
});

app.listen(port, () => {
  console.log(`🚀 API server running on http://localhost:${port}`);
  console.log(`📡 Gemini API configured: ${!!genAI}`);
});
