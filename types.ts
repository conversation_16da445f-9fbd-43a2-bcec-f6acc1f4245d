export enum MessageSender {
  USER = 'user',
  AI = 'ai',
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: MessageSender;
  timestamp: Date;
  isError?: boolean;
}

export interface Scenario {
  id:string;
  title: string;
  prompt: string; // Initial prompt for the user/AI to start the argument
  icon?: React.FC<React.SVGProps<SVGSVGElement>>; // Optional icon for the scenario
}

// For constructing history for the API
export interface ApiChatMessage {
  role: 'user' | 'model';
  parts: { text: string }[];
}

export interface GeminiApiRequestBody {
  history: ApiChatMessage[];
  newMessage: string;
  systemInstruction: string;
}

export const messageSenderToApiRole = (sender: MessageSender): 'user' | 'model' => {
  return sender === MessageSender.USER ? 'user' : 'model';
};