# 吵架包赢 - AI Debate App

This is an AI-powered debate application where you can practice your argumentation skills against an AI opponent.

## Run Locally

**Prerequisites:** Node.js

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key

3. Run the app (this will start both the API server and the frontend):
   ```bash
   npm run dev
   ```

The app will be available at `http://localhost:5173` and the API server will run on `http://localhost:3001`.

## Development

- `npm run dev` - Starts both the API server and frontend concurrently
- `npm run server` - Starts only the API server
- `npm run client` - Starts only the frontend (Vite dev server)

## Features

- Choose from various debate topics
- Real-time AI responses using Google's Gemini AI
- Clean, responsive UI with Tailwind CSS
- Chinese language support

## Deploy to Vercel

1. Connect your GitHub repository to Vercel
2. Set the environment variable `GEMINI_API_KEY` in your Vercel project settings
3. Deploy automatically on push to main branch

### Environment Variables for Vercel

Make sure to set the following environment variable in your Vercel project:
- `GEMINI_API_KEY`: Your Google Gemini API key
