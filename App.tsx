
import React, { useState, useEffect, useCallback } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { ScenarioSelector } from './components/ScenarioSelector';
import { exchangeMessagesWithAI } from './services/geminiService';
import { ChatMessage, MessageSender, Scenario } from './types';
import { ARGUMENT_SCENARIOS, SYSTEM_INSTRUCTION } from './constants';

// API_KEY is no longer used directly in the frontend.
// It will be configured in Vercel environment variables for the serverless function.

const App: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentScenario, setCurrentScenario] = useState<Scenario | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  // chatSession state is removed as the backend API will be stateless or manage session differently
  const [showScenarioSelector, setShowScenarioSelector] = useState<boolean>(true);
  // isApiKeyMissing state is removed, backend handles API key issues.

  // Effect to check for general backend availability (optional, simple check)
  useEffect(() => {
    // You could add a health check to your API endpoint if desired
    // For now, we assume the backend is available if no errors occur during calls.
  }, []);

  const startNewArgument = useCallback(async (scenario: Scenario) => {
    setShowScenarioSelector(false);
    setCurrentScenario(scenario);
    setMessages([]); // Clear previous messages
    setIsLoading(true);
    setError(null);

    const initialUserMessageForAI: ChatMessage = {
      id: Date.now().toString(),
      text: scenario.prompt, // User's initial statement for the debate topic
      sender: MessageSender.USER, // Treated as the first user message to kick off AI's response
      timestamp: new Date(),
    };
    
    // Display the scenario prompt as the AI's first "statement" or topic intro
    const scenarioIntroMessage: ChatMessage = {
      id: (Date.now() -1 ).toString(), // ensure unique id
      text: `辩题开始：${scenario.title}\n“${scenario.prompt}”`,
      sender: MessageSender.AI, // Or a system message type if you add one
      timestamp: new Date(Date.now() -1),
    };
    setMessages([scenarioIntroMessage]);

    try {
      // The AI will respond to the scenario's prompt, which we frame as the user's first "turn"
      const aiResponseText = await exchangeMessagesWithAI(
        [], // No prior history for the very first turn for this scenario
        scenario.prompt, // The scenario prompt acts as the "new message" from the user
        SYSTEM_INSTRUCTION
      );
      
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponseText,
        sender: MessageSender.AI,
        timestamp: new Date(),
      };
      // Add AI's response. The scenario prompt itself isn't added again as a user message here,
      // because AI is responding to it. The user's first *typed* message will be handled by handleSendMessage.
      setMessages(prevMessages => [...prevMessages, aiMessage]);

    } catch (err) {
      console.error("无法开始辩论:", err);
      const errorMessage = err instanceof Error ? err.message : "开始辩论时发生未知错误。";
      setError(errorMessage);
      // Add error message to chat
       const errorAiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `错误: ${errorMessage}`,
        sender: MessageSender.AI,
        timestamp: new Date(),
        isError: true,
      };
      setMessages(prevMessages => [...prevMessages, errorAiMessage]);
      setShowScenarioSelector(true); 
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSendMessage = useCallback(async (inputText: string) => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: MessageSender.USER,
      timestamp: new Date(),
    };
    
    // Include the user's new message in the history for the API call
    const currentHistory = [...messages, userMessage];
    setMessages(currentHistory);
    setIsLoading(true);
    setError(null);

    try {
      // Pass the relevant message history to the AI
      const aiResponseText = await exchangeMessagesWithAI(
        currentHistory.filter(m => !m.isError), // Send history without error messages
        inputText, // This is technically redundant if history includes it, but fine for clarity. API will use history.
        SYSTEM_INSTRUCTION
      );
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponseText,
        sender: MessageSender.AI,
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    } catch (err) {
      console.error("发送消息失败:", err);
      const errorMessage = err instanceof Error ? err.message : "未能获取 AI 回复。";
      setError(errorMessage);
      const errorAiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `错误: ${errorMessage}`,
        sender: MessageSender.AI,
        timestamp: new Date(),
        isError: true,
      };
      setMessages(prevMessages => [...prevMessages, errorAiMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [messages, isLoading]);

  const handleEndArgument = () => {
    setShowScenarioSelector(true);
    setCurrentScenario(null);
    setMessages([]);
    setError(null);
  };
  
  // API Key check is removed from frontend. If API calls fail, error state will be set.
  // The "API 密钥缺失" message for direct client-side key is no longer applicable.
  // Errors will be more general (e.g., "Failed to connect to server").

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-slate-200 via-slate-100 to-gray-200 p-2 sm:p-4 transition-all duration-500 ease-in-out">
      <div className="w-full max-w-2xl bg-white/80 backdrop-blur-xl shadow-2xl rounded-2xl overflow-hidden flex flex-col h-[calc(100vh-2rem)] sm:h-[calc(100vh-4rem)] min-h-[500px] max-h-[800px] border border-slate-300/20">
        <header className="p-4 sm:p-5 border-b border-slate-200/80 bg-gradient-to-b from-slate-50/70 to-slate-100/70 rounded-t-2xl">
          <h1 className="text-xl sm:text-2xl font-bold text-center text-slate-800 tracking-tight">
            吵架包赢
          </h1>
          {currentScenario && !showScenarioSelector && (
             <p className="text-xs sm:text-sm text-center text-slate-600 mt-1">辩题: {currentScenario.title}</p>
          )}
        </header>

        {error && ( // General error display
          <div className="p-3 sm:p-4 bg-red-100 border-b border-red-200 text-red-700">
            <p className="text-sm text-center">{error}</p>
          </div>
        )}

        {showScenarioSelector ? (
          <ScenarioSelector scenarios={ARGUMENT_SCENARIOS} onSelectScenario={startNewArgument} />
        ) : (
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            onEndArgument={handleEndArgument}
            currentTopic={currentScenario?.title || "辩论"}
          />
        )}
      </div>
      <footer className="text-center mt-4 sm:mt-6 text-xs text-slate-500/80">
        <p>&copy; {new Date().getFullYear()} 吵架包赢. 由 Gemini 驱动.</p>
      </footer>
    </div>
  );
};

export default App;
