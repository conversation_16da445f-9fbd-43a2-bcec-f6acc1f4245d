
import React, { useState, useEffect, useCallback } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { ScenarioSelector } from './components/ScenarioSelector';
import { QuickReplyButtons } from './components/QuickReplyButtons';
import { DebateStats } from './components/DebateStats';
import { SettingsPanel } from './components/SettingsPanel';
import { exchangeMessagesWithAI } from './services/geminiService';
import { ChatMessage, MessageSender, Scenario } from './types';
import { ARGUMENT_SCENARIOS, SYSTEM_INSTRUCTION } from './constants';

// API_KEY is no longer used directly in the frontend.
// It will be configured in Vercel environment variables for the serverless function.

const App: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentScenario, setCurrentScenario] = useState<Scenario | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showScenarioSelector, setShowScenarioSelector] = useState<boolean>(true);

  // 新功能状态
  const [debateStartTime, setDebateStartTime] = useState<Date>(new Date());
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [showQuickReplies, setShowQuickReplies] = useState<boolean>(true);
  const [showStats, setShowStats] = useState<boolean>(true);
  const [statsCollapsed, setStatsCollapsed] = useState<boolean>(false);
  const [aiPersonality, setAiPersonality] = useState<'aggressive' | 'moderate' | 'friendly'>('aggressive');
  const [typingSpeed, setTypingSpeed] = useState<number>(30);

  // Effect to check for general backend availability (optional, simple check)
  useEffect(() => {
    // You could add a health check to your API endpoint if desired
    // For now, we assume the backend is available if no errors occur during calls.
  }, []);

  // 根据 AI 性格调整系统指令
  const getPersonalityInstruction = (personality: 'aggressive' | 'moderate' | 'friendly') => {
    const personalityInstructions = {
      aggressive: "你是一个极其自信、近乎自负的知识分子，喜欢唇枪舌战。你的回复应该犀利尖锐，不留情面，充满讽刺和智慧。",
      moderate: "你是一个理性的辩论者，逻辑清晰，有理有据。你会坚持自己的观点，但保持理性和客观。",
      friendly: "你是一个友善的讨论者，温和但坚定。你会循循善诱，用温和的方式表达不同观点。"
    };
    return `${SYSTEM_INSTRUCTION}\n\n性格设定：${personalityInstructions[personality]}`;
  };

  const startNewArgument = useCallback(async (scenario: Scenario) => {
    setShowScenarioSelector(false);
    setCurrentScenario(scenario);
    setMessages([]); // Clear previous messages
    setDebateStartTime(new Date()); // 重置辩论开始时间
    setIsLoading(true);
    setError(null);

    const initialUserMessageForAI: ChatMessage = {
      id: Date.now().toString(),
      text: scenario.prompt, // User's initial statement for the debate topic
      sender: MessageSender.USER, // Treated as the first user message to kick off AI's response
      timestamp: new Date(),
    };

    // Display the scenario prompt as the AI's first "statement" or topic intro
    const scenarioIntroMessage: ChatMessage = {
      id: (Date.now() -1 ).toString(), // ensure unique id
      text: `辩题开始：${scenario.title}\n“${scenario.prompt}”`,
      sender: MessageSender.AI, // Or a system message type if you add one
      timestamp: new Date(Date.now() -1),
    };
    setMessages([scenarioIntroMessage]);

    try {
      // The AI will respond to the scenario's prompt, which we frame as the user's first "turn"
      const aiResponseText = await exchangeMessagesWithAI(
        [], // No prior history for the very first turn for this scenario
        scenario.prompt, // The scenario prompt acts as the "new message" from the user
        getPersonalityInstruction(aiPersonality)
      );

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponseText,
        sender: MessageSender.AI,
        timestamp: new Date(),
      };
      // Add AI's response. The scenario prompt itself isn't added again as a user message here,
      // because AI is responding to it. The user's first *typed* message will be handled by handleSendMessage.
      setMessages(prevMessages => [...prevMessages, aiMessage]);

    } catch (err) {
      console.error("无法开始辩论:", err);
      const errorMessage = err instanceof Error ? err.message : "开始辩论时发生未知错误。";
      setError(errorMessage);
      // Add error message to chat
       const errorAiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `错误: ${errorMessage}`,
        sender: MessageSender.AI,
        timestamp: new Date(),
        isError: true,
      };
      setMessages(prevMessages => [...prevMessages, errorAiMessage]);
      setShowScenarioSelector(true);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSendMessage = useCallback(async (inputText: string) => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: MessageSender.USER,
      timestamp: new Date(),
    };

    // Include the user's new message in the history for the API call
    const currentHistory = [...messages, userMessage];
    setMessages(currentHistory);
    setIsLoading(true);
    setError(null);

    try {
      // Pass the relevant message history to the AI
      const aiResponseText = await exchangeMessagesWithAI(
        currentHistory.filter(m => !m.isError), // Send history without error messages
        inputText, // This is technically redundant if history includes it, but fine for clarity. API will use history.
        getPersonalityInstruction(aiPersonality)
      );
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponseText,
        sender: MessageSender.AI,
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    } catch (err) {
      console.error("发送消息失败:", err);
      const errorMessage = err instanceof Error ? err.message : "未能获取 AI 回复。";
      setError(errorMessage);
      const errorAiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `错误: ${errorMessage}`,
        sender: MessageSender.AI,
        timestamp: new Date(),
        isError: true,
      };
      setMessages(prevMessages => [...prevMessages, errorAiMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [messages, isLoading]);

  const handleEndArgument = () => {
    setShowScenarioSelector(true);
    setCurrentScenario(null);
    setMessages([]);
    setError(null);
  };

  // 新功能处理函数
  const handleQuickReply = useCallback((text: string) => {
    handleSendMessage(text);
  }, [handleSendMessage]);

  const handleEditMessage = useCallback((messageId: string, newText: string) => {
    setMessages(prevMessages =>
      prevMessages.map(msg =>
        msg.id === messageId ? { ...msg, text: newText } : msg
      )
    );
  }, []);

  const handleDeleteMessage = useCallback((messageId: string) => {
    setMessages(prevMessages =>
      prevMessages.filter(msg => msg.id !== messageId)
    );
  }, []);

  const toggleDarkMode = useCallback(() => {
    setIsDarkMode(prev => !prev);
  }, []);

  const toggleStatsCollapse = useCallback(() => {
    setStatsCollapsed(prev => !prev);
  }, []);

  // API Key check is removed from frontend. If API calls fail, error state will be set.
  // The "API 密钥缺失" message for direct client-side key is no longer applicable.
  // Errors will be more general (e.g., "Failed to connect to server").

  return (
    <div className={`flex min-h-screen transition-all duration-500 ease-in-out ${
      isDarkMode
        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-slate-900'
        : 'bg-gradient-to-br from-slate-200 via-slate-100 to-gray-200'
    } p-2 sm:p-4`}>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className={`w-full max-w-2xl backdrop-blur-xl shadow-2xl rounded-2xl overflow-hidden flex flex-col h-[calc(100vh-2rem)] sm:h-[calc(100vh-4rem)] min-h-[500px] max-h-[800px] border ${
          isDarkMode
            ? 'bg-gray-800/80 border-gray-700/20'
            : 'bg-white/80 border-slate-300/20'
        }`}>

          {/* 标题栏 */}
          <header className={`p-4 sm:p-5 border-b rounded-t-2xl ${
            isDarkMode
              ? 'border-gray-700/80 bg-gradient-to-b from-gray-800/70 to-gray-900/70'
              : 'border-slate-200/80 bg-gradient-to-b from-slate-50/70 to-slate-100/70'
          }`}>
            <div className="flex items-center justify-between">
              <h1 className={`text-xl sm:text-2xl font-bold tracking-tight ${
                isDarkMode ? 'text-white' : 'text-slate-800'
              }`}>
                吵架包赢
              </h1>

              {/* 设置按钮 */}
              <button
                onClick={() => setShowSettings(true)}
                className={`p-2 rounded-full transition-colors ${
                  isDarkMode
                    ? 'hover:bg-gray-700 text-gray-300 hover:text-white'
                    : 'hover:bg-gray-100 text-gray-600 hover:text-gray-800'
                }`}
                title="设置"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>

            {currentScenario && !showScenarioSelector && (
               <p className={`text-xs sm:text-sm text-center mt-1 ${
                 isDarkMode ? 'text-gray-300' : 'text-slate-600'
               }`}>
                 辩题: {currentScenario.title}
               </p>
            )}
          </header>

          {error && ( // General error display
            <div className={`p-3 sm:p-4 border-b ${
              isDarkMode
                ? 'bg-red-900/50 border-red-800 text-red-300'
                : 'bg-red-100 border-red-200 text-red-700'
            }`}>
              <p className="text-sm text-center">{error}</p>
            </div>
          )}

          {/* 主内容 */}
          <div className="flex-1 flex">
            {showScenarioSelector ? (
              <ScenarioSelector scenarios={ARGUMENT_SCENARIOS} onSelectScenario={startNewArgument} />
            ) : (
              <div className="flex-1 flex flex-col">
                <ChatInterface
                  messages={messages}
                  onSendMessage={handleSendMessage}
                  isLoading={isLoading}
                  onEndArgument={handleEndArgument}
                  currentTopic={currentScenario?.title || "辩论"}
                  onEditMessage={handleEditMessage}
                  onDeleteMessage={handleDeleteMessage}
                />

                {/* 快速回复按钮 */}
                {showQuickReplies && !showScenarioSelector && (
                  <div className={`p-3 border-t ${
                    isDarkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50/50'
                  }`}>
                    <QuickReplyButtons
                      onQuickReply={handleQuickReply}
                      currentTopic={currentScenario?.title || ""}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 页脚 */}
        <footer className={`text-center mt-4 sm:mt-6 text-xs ${
          isDarkMode ? 'text-gray-400' : 'text-slate-500/80'
        }`}>
          <p>&copy; {new Date().getFullYear()} 吵架包赢. 由 Gemini 驱动.</p>
        </footer>
      </div>

      {/* 侧边统计面板 */}
      {showStats && !showScenarioSelector && (
        <div className="hidden lg:block w-80 ml-4">
          <DebateStats
            messages={messages}
            startTime={debateStartTime}
            currentTopic={currentScenario?.title || ""}
            isCollapsed={statsCollapsed}
            onToggleCollapse={toggleStatsCollapse}
          />
        </div>
      )}

      {/* 设置面板 */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        isDarkMode={isDarkMode}
        onToggleDarkMode={toggleDarkMode}
        aiPersonality={aiPersonality}
        onPersonalityChange={setAiPersonality}
        typingSpeed={typingSpeed}
        onTypingSpeedChange={setTypingSpeed}
        showQuickReplies={showQuickReplies}
        onToggleQuickReplies={() => setShowQuickReplies(prev => !prev)}
        showStats={showStats}
        onToggleStats={() => setShowStats(prev => !prev)}
      />
    </div>
  );
};

export default App;
