import { ChatMessage, ApiChatMessage, GeminiApiRequestBody, messageSenderToApiRole } from '../types';
import { SYSTEM_INSTRUCTION } from '../constants'; // SYSTEM_INSTRUCTION is still sourced from frontend

// This service now calls our own backend API endpoint

export const exchangeMessagesWithAI = async (
  history: ChatMessage[],
  newMessageText: string,
  systemInstruction: string = SYSTEM_INSTRUCTION // Allow overriding system instruction if needed per call
): Promise<string> => {
  const apiHistory: ApiChatMessage[] = history.map(msg => ({
    role: messageSenderToApiRole(msg.sender),
    parts: [{ text: msg.text }],
  }));

  const body: GeminiApiRequestBody = {
    history: apiHistory,
    newMessage: newMessageText,
    systemInstruction: systemInstruction,
  };

  try {
    const response = await fetch('/api/gemini', { // Calls our new Vercel serverless function
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
      throw new Error(errorData.error || `API request failed with status ${response.status}`);
    }

    const data = await response.json();
    if (typeof data.text === 'string') {
      return data.text;
    }
    throw new Error("AI response format was unexpected.");

  } catch (error) {
    console.error('Error communicating with backend API:', error);
    throw new Error(`Failed to get AI response: ${error instanceof Error ? error.message : String(error)}`);
  }
};
